# CSV to JSON Converter

A FileDuck app that converts CSV files to JSON format with customizable options.

## Features

- **Drag & Drop Interface**: Easy file selection with visual feedback
- **Multiple File Support**: Convert multiple CSV files at once
- **Customizable JSON Structure**: Choose from different JSON orientations
- **Flexible Formatting**: Control indentation and date formats
- **Progress Tracking**: Real-time conversion progress with error handling
- **File Management**: Automatic saving to 'converted' folder with easy access

## File Structure

```
csv-to-json/
├── dist/
│   ├── main.py      # Python conversion logic
│   └── config.json  # App configuration and UI definition
└── README.md        # This file
```

## Dependencies

- **pandas**: For CSV reading and JSON conversion
- **json**: For JSON handling (built-in)
- **pathlib**: For file path operations (built-in)

## Configuration Options

The app supports the following JSON conversion options:

### JSON Structure (orient)
- **records**: Array of objects (default) - `[{col1: val1, col2: val2}, ...]`
- **index**: Object with row indices - `{0: {col1: val1, col2: val2}, ...}`
- **values**: Array of arrays - `[[val1, val2], [val3, val4], ...]`
- **split**: Separate columns and data - `{columns: [...], data: [...]}`

### Formatting Options
- **Indentation**: 0 (compact), 2 spaces (default), or 4 spaces
- **Date Format**: ISO format (recommended) or epoch timestamps

## Usage in FileDuck

1. Install the app from a ZIP file containing the `dist/` folder
2. The app will automatically install pandas dependency
3. Select CSV files using drag & drop or file browser
4. Configure conversion options as needed
5. Click "Convert to JSON" to process files
6. Access converted files in the 'converted' folder

## Debug Testing

To test the UI in a browser environment:
1. Start the FileDuck development server
2. Navigate to `/debug-csv-to-json` in the app
3. The debug interface will show how the app looks and behaves

## Technical Details

- **Runtime**: Python 3.x with spawn-based execution
- **Input**: CSV files via FileDuck's file handling system
- **Output**: JSON files saved to `./converted/` directory
- **Error Handling**: Comprehensive error reporting with user-friendly messages
- **UI Framework**: React with Shadcn UI components and Tailwind CSS
