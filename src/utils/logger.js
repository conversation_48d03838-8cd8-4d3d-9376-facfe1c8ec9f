// Browser-compatible logger for React frontend
// Uses console with Gen-Z emojis and styling

class BrowserLogger {
  constructor(scope = '') {
    this.scope = scope;
  }

  _formatMessage(emoji, label, message, ...args) {
    const timestamp = new Date().toLocaleTimeString();
    const scopeStr = this.scope ? `[${this.scope}]` : '';
    const prefix = `${scopeStr} › ${emoji} ${label}`;

    if (typeof message === 'object') {
      console.log(`${prefix}`, message, ...args);
    } else {
      console.log(`${prefix} ${message}`, ...args);
    }
  }

  // Success and positive actions
  success(message, ...args) {
    this._formatMessage('✨', 'success', message, ...args);
  }

  complete(message, ...args) {
    this._formatMessage('🎉', 'complete', message, ...args);
  }

  // Information and status
  info(message, ...args) {
    this._formatMessage('💡', 'info', message, ...args);
  }

  note(message, ...args) {
    this._formatMessage('📝', 'note', message, ...args);
  }

  // Process and loading states
  pending(message, ...args) {
    this._formatMessage('⏳', 'pending', message, ...args);
  }

  await(message, ...args) {
    this._formatMessage('⌛', 'await', message, ...args);
  }

  watch(message, ...args) {
    this._formatMessage('👀', 'watch', message, ...args);
  }

  start(message, ...args) {
    this._formatMessage('🚀', 'start', message, ...args);
  }

  // Warnings and cautions
  warn(message, ...args) {
    this._formatMessage('⚠️', 'warn', message, ...args);
  }

  pause(message, ...args) {
    this._formatMessage('⏸️', 'pause', message, ...args);
  }

  // Errors and failures
  error(message, ...args) {
    this._formatMessage('💥', 'error', message, ...args);
  }

  fatal(message, ...args) {
    this._formatMessage('💀', 'fatal', message, ...args);
  }

  // Debug and development
  debug(message, ...args) {
    this._formatMessage('🐛', 'debug', message, ...args);
  }

  // Special FileDuck specific loggers
  fileduck(message, ...args) {
    this._formatMessage('🦆', 'fileduck', message, ...args);
  }

  app(message, ...args) {
    this._formatMessage('📱', 'app', message, ...args);
  }

  install(message, ...args) {
    this._formatMessage('📦', 'install', message, ...args);
  }

  uninstall(message, ...args) {
    this._formatMessage('🗑️', 'uninstall', message, ...args);
  }

  python(message, ...args) {
    this._formatMessage('🐍', 'python', message, ...args);
  }

  file(message, ...args) {
    this._formatMessage('📄', 'file', message, ...args);
  }

  api(message, ...args) {
    this._formatMessage('🌐', 'api', message, ...args);
  }

  database(message, ...args) {
    this._formatMessage('🗄️', 'database', message, ...args);
  }

  // Create scoped logger
  scope(scopeName) {
    return new BrowserLogger(scopeName);
  }
}

// Create the main logger instance
const logger = new BrowserLogger();

// Export both the configured logger and the class for custom instances
export {
  logger,
  BrowserLogger
};

export const createLogger = (scope) => logger.scope(scope);
