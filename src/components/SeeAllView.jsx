import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, CheckCircle, Loader2 } from 'lucide-react';
import { PageWrapper } from './ui/page-wrapper';
import { Mac<PERSON>Button } from './ui/macos-button';
import { Typography } from '../utils/typography';
import { useStoreData } from '../contexts/StoreDataContext';

const SeeAllView = () => {
  const { section } = useParams();
  const navigate = useNavigate();
  const { 
    trendingApps, 
    popularApps, 
    newApps, 
    recommendedApps,
    loadingTrending,
    loadingPopular,
    loadingNew,
    loadingRecommended
  } = useStoreData();

  const getSectionData = () => {
    switch (section) {
      case 'trending':
        return { apps: trendingApps, loading: loadingTrending, title: 'See what\'s trending' };
      case 'popular':
        return { apps: popularApps, loading: loadingPopular, title: 'See what\'s popular' };
      case 'new':
        return { apps: newApps, loading: loadingNew, title: 'See what\'s new' };
      case 'recommended':
        return { apps: recommendedApps, loading: loadingRecommended, title: 'Recommended for you' };
      default:
        return { apps: [], loading: false, title: 'Apps' };
    }
  };

  const { apps, loading, title } = getSectionData();

  const AppCard = ({ app }) => (
    <motion.div
      className="relative bg-macos-surface border border-macos-border rounded-xl p-6 hover:bg-macos-elevated transition-colors cursor-pointer"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => navigate(`/store/app/${app.app_id}`)}
    >
      <div className="flex flex-col items-center text-center space-y-4">
        {/* App Icon */}
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0">
          <span className="text-white text-2xl font-bold">
            {app.name?.charAt(0) || 'A'}
          </span>
        </div>

        {/* App Content */}
        <div className="flex-1 min-w-0 w-full">
          <h3 className={`${Typography.body} font-medium mb-2 truncate`}>
            {app.name}
          </h3>
          <p className={`${Typography.caption} mb-3 line-clamp-2 text-center`}>
            {app.description}
          </p>
          <div className="flex items-center justify-center space-x-2 mb-4">
            <span className={`${Typography.footnote} text-macos-text-tertiary`}>
              Free
            </span>
          </div>
        </div>

        {/* Get Button */}
        <div className="w-full">
          <MacOSButton
            variant="secondary"
            size="sm"
            className="rounded-full px-6 w-full"
            onClick={(e) => {
              e.stopPropagation();
              // Handle install
            }}
          >
            Get
          </MacOSButton>
        </div>
      </div>
    </motion.div>
  );

  const ShimmerCard = () => (
    <div className="bg-macos-surface border border-macos-border rounded-xl p-6 animate-pulse">
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="w-20 h-20 bg-macos-border rounded-2xl"></div>
        <div className="w-full space-y-2">
          <div className="h-4 bg-macos-border rounded mx-auto w-3/4"></div>
          <div className="h-3 bg-macos-border rounded mx-auto w-full"></div>
          <div className="h-3 bg-macos-border rounded mx-auto w-2/3"></div>
          <div className="h-3 bg-macos-border rounded mx-auto w-1/3 mt-3"></div>
        </div>
        <div className="w-full h-8 bg-macos-border rounded-full"></div>
      </div>
    </div>
  );

  return (
    <PageWrapper>
      <div className="p-8 h-full overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center space-x-4 mb-8">
            <MacOSButton
              variant="secondary"
              onClick={() => navigate('/store')}
              className="rounded-full"
            >
              <ArrowLeft className="h-4 w-4" />
            </MacOSButton>
            <h1 className={Typography.largeTitle}>{title}</h1>
          </div>

          {/* apps Grid - 3 Column Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              // Show shimmer loading
              Array.from({ length: 9 }).map((_, index) => (
                <ShimmerCard key={`shimmer-${index}`} />
              ))
            ) : apps.length > 0 ? (
              apps.map((app) => (
                <AppCard key={app.app_id || app.id} app={app} />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <h3 className={Typography.title2}>No Apps Found</h3>
                <p className={Typography.bodySecondary}>
                  No apps available in this section.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default SeeAllView;
