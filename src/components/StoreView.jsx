import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import {
  Search,
  Star,
  ChevronRight,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Mac<PERSON>Button } from './ui/macos-button';
import { Typography } from '../utils/typography';
import { useStoreData } from '../contexts/StoreDataContext';
import { logger } from '../utils/logger';

const StoreView = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [installing, setInstalling] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const [installationProgress, setInstallationProgress] = useState({});
  const [toast, setToast] = useState(null);

  // Use cached data from context
  const {
    trendingApps,
    popularApps,
    newApps,
    recommendedApps,
    searchResults,
    installedUtilities,
    loadingTrending,
    loadingPopular,
    loadingNew,
    loadingRecommended,
    loadingSearch,
    searchApps,
    loadInstalledUtilities
  } = useStoreData();

  const containerRef = useRef(null);
  const headerRef = useRef(null);
  const { scrollY } = useScroll({ container: containerRef });

  // API Base URL
  const API_BASE_URL = 'http://localhost:8000';
  const USER_ID = 'user_test_123';

  // Toast management
  const showToast = (message, type = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // API call functions
  const callInstallAPI = async (appId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/${appId}/install`, {
        method: 'POST',
        headers: {
          'user-id': USER_ID,
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error('Failed to record installation');
      return await response.json();
    } catch (error) {
      logger.error('Install API call failed:', error);
    }
  };

  const callUninstallAPI = async (appId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/${appId}/uninstall`, {
        method: 'POST',
        headers: {
          'user-id': USER_ID,
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error('Failed to record uninstallation');
      return await response.json();
    } catch (error) {
      logger.error('Uninstall API call failed:', error);
    }
  };

  const callRunAPI = async (appId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/playstore/apps/v1/${appId}/run`, {
        method: 'POST',
        headers: {
          'user-id': USER_ID,
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error('Failed to record app run');
      return await response.json();
    } catch (error) {
      logger.error('Run API call failed:', error);
    }
  };

  // Available apps in store (fallback data)
  const storeApps = [];

  // Data is now loaded from context, no need to fetch here

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery) {
        searchApps(searchQuery);
      } else {
        searchApps(''); // This will clear search results in the context
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, searchApps]);

  // Handle scroll animations
  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const scrollTop = containerRef.current.scrollTop;
        const threshold = 120; // Adjust this value to control when the animation triggers
        setIsScrolled(scrollTop > threshold);
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Animation transforms
  const headerOpacity = useTransform(scrollY, [0, 120], [1, 0]);
  const subtitleOpacity = useTransform(scrollY, [0, 80], [1, 0]);
  const searchBarY = useTransform(scrollY, [0, 120], [0, -60]);

  // Check if a utility is already installed
  const isUtilityInstalled = (appId) => {
    return installedUtilities.some(utility =>
      utility.id.includes(appId) || utility.name.toLowerCase().includes(appId.toLowerCase())
    );
  };

  // Section-based app organization using API data with fallback
  const getAppsForSection = (sectionType) => {
    let apps = [];
    switch (sectionType) {
      case 'trending':
        apps = Array.isArray(trendingApps) && trendingApps.length > 0 ? trendingApps : storeApps.filter(app => app.isTrending);
        break;
      case 'popular':
        apps = Array.isArray(popularApps) && popularApps.length > 0 ? popularApps : storeApps.filter(app => app.isFeatured);
        break;
      case 'new':
        apps = Array.isArray(newApps) && newApps.length > 0 ? newApps : storeApps.filter(app => app.isNew);
        break;
      case 'recommended':
        apps = Array.isArray(recommendedApps) && recommendedApps.length > 0 ? recommendedApps : storeApps;
        break;
      default:
        apps = storeApps;
    }
    return apps.slice(0, 4); // Limit to 4 apps per section
  };

  const getLoadingStateForSection = (sectionType) => {
    // Don't show loading if we have fallback data to display
    const hasApiData = () => {
      switch (sectionType) {
        case 'trending':
          return Array.isArray(trendingApps) && trendingApps.length > 0;
        case 'popular':
          return Array.isArray(popularApps) && popularApps.length > 0;
        case 'new':
          return Array.isArray(newApps) && newApps.length > 0;
        case 'recommended':
          return Array.isArray(recommendedApps) && recommendedApps.length > 0;
        default:
          return false;
      }
    };

    // Only show loading if we don't have API data and we're still loading
    if (hasApiData()) return false;

    switch (sectionType) {
      case 'trending':
        return loadingTrending;
      case 'popular':
        return loadingPopular;
      case 'new':
        return loadingNew;
      case 'recommended':
        return loadingRecommended;
      default:
        return false;
    }
  };

  const getSearchResults = () => {
    return Array.isArray(searchResults) ? searchResults : [];
  };

  const handleInstall = async (app) => {
    const appId = app.app_id || app.id;
    if (installing === appId) return;

    setInstalling(appId);
    setInstallationProgress(prev => ({ ...prev, [appId]: 'installing' }));

    try {
      logger.install('Installing app:', appId);

      // Use electron API to install the utility
      if (window.electronAPI) {
        let result;
        let downloadUrl;

        // Check if app has installer_url from API
        if (app.installer_url) {
          downloadUrl = app.installer_url;
        } else {
          throw new Error('No download URL available');
        }

        // Always use the remote installation method
        // The contextBridge will handle local vs remote URLs appropriately
        result = await window.electronAPI.installUtility(downloadUrl);

        if (result.success) {
          logger.success('App installed successfully:', result.utilityId);
          setInstallationProgress(prev => ({ ...prev, [appId]: 'success' }));
          showToast(`${app.app_name || app.name} installed successfully!`, 'success');

          // Call install API to track installation
          await callInstallAPI(appId);

          // Refresh installed utilities list using context
          await loadInstalledUtilities();

          // Clear the installation progress after a short delay to show the "Open" button
          setTimeout(() => {
            setInstallationProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[appId];
              return newProgress;
            });
          }, 1500);
        } else {
          logger.error('Installation failed:', result.error);
          setInstallationProgress(prev => ({ ...prev, [appId]: 'failed' }));
          showToast(`Failed to install ${app.app_name || app.name}. Try again.`, 'error');

          // Clear failed state after 3 seconds
          setTimeout(() => {
            setInstallationProgress(prev => {
              const newProgress = { ...prev };
              delete newProgress[appId];
              return newProgress;
            });
          }, 3000);
        }
      } else {
        logger.info('Electron API not available - running in browser mode');
        setInstallationProgress(prev => ({ ...prev, [appId]: 'failed' }));
        showToast('Installation not available in browser mode', 'error');

        // Clear failed state after 3 seconds
        setTimeout(() => {
          setInstallationProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[appId];
            return newProgress;
          });
        }, 3000);
      }
    } catch (error) {
      logger.error('Failed to install app:', error);
      setInstallationProgress(prev => ({ ...prev, [appId]: 'failed' }));
      showToast(`Failed to install ${app.app_name || app.name}. Try again.`, 'error');

      // Clear failed state after 3 seconds
      setTimeout(() => {
        setInstallationProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[appId];
          return newProgress;
        });
      }, 3000);
    } finally {
      setInstalling(null);
    }
  };

  const handleOpen = async (app) => {
    const appId = app.app_id || app.id;

    // Find the installed utility ID
    const installedUtility = installedUtilities.find(utility =>
      utility.id.includes(appId) || utility.name.toLowerCase().includes(appId.toLowerCase())
    );

    if (installedUtility) {
      // Call run API to track app usage
      await callRunAPI(appId);

      // Remember that we came from Store tab
      sessionStorage.setItem('sourceTab', '/store');
      navigate(`/app/${installedUtility.id}`);
    }
  };

  // Shimmer loading component
  const ShimmerCard = ({ showDivider = true }) => (
    <div className="relative">
      <div className="flex items-center py-3 px-0 animate-pulse">
        <div className="w-16 h-16 bg-macos-border rounded-xl mr-4 flex-shrink-0"></div>
        <div className="flex-1 min-w-0 mr-3">
          <div className="h-4 bg-macos-border rounded mb-2 w-3/4"></div>
          <div className="h-3 bg-macos-border rounded mb-2 w-1/2"></div>
          <div className="flex items-center space-x-3">
            <div className="h-3 bg-macos-border rounded w-12"></div>
            <div className="h-3 bg-macos-border rounded w-8"></div>
            <div className="h-3 bg-macos-border rounded w-16"></div>
          </div>
        </div>
        <div className="flex-shrink-0">
          <div className="h-8 bg-macos-border rounded-full w-16"></div>
        </div>
      </div>
      {showDivider && (
        <div className="absolute bottom-0 left-20 right-0 h-px bg-macos-border"></div>
      )}
    </div>
  );

  // StoreSection component for reusable sections
  const StoreSection = ({ title, sectionType, showSeeAll = true }) => {
    const apps = getAppsForSection(sectionType);
    const isLoading = getLoadingStateForSection(sectionType);

    return (
      <div className="mb-10">
        <div className="flex items-center justify-between mb-4">
          <h2 className={Typography.title2}>
            {title}
          </h2>
          {showSeeAll && (
            <motion.button
              className="flex items-center text-system-blue hover:text-system-blue/80 text-sm font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => navigate(`/store/see-all/${sectionType}`)}
            >
              See All
              <ChevronRight className="h-4 w-4 ml-1" />
            </motion.button>
          )}
        </div>

        {/* 2-column grid layout */}
        <div className="grid grid-cols-2 gap-x-8">
          <div className="space-y-0">
            {isLoading ? (
              // Show shimmer loading for left column
              Array.from({ length: 2 }).map((_, index) => (
                <ShimmerCard key={`shimmer-left-${index}`} showDivider={index < 1} />
              ))
            ) : (
              apps.filter((_, index) => index % 2 === 0).map((app, index, filteredApps) => (
                <AppCard
                  key={app.app_id || app.id}
                  app={app}
                  showDivider={index < filteredApps.length - 1}
                />
              ))
            )}
          </div>
          <div className="space-y-0">
            {isLoading ? (
              // Show shimmer loading for right column
              Array.from({ length: 2 }).map((_, index) => (
                <ShimmerCard key={`shimmer-right-${index}`} showDivider={index < 1} />
              ))
            ) : (
              apps.filter((_, index) => index % 2 === 1).map((app, index, filteredApps) => (
                <AppCard
                  key={app.app_id || app.id}
                  app={app}
                  showDivider={index < filteredApps.length - 1}
                />
              ))
            )}
          </div>
        </div>
      </div>
    );
  };

  // AppCard component - Apple App Store exact style (no cards, clean layout)
  const AppCard = ({ app, showDivider = true }) => {
    // Handle both API data and fallback data formats
    const appId = app.app_id || app.id;
    const appName = app.app_name || app.name;
    const appDescription = app.short_description || app.description;
    const appRating = app.rating || '4.0';
    const appDownloads = app.installs || app.downloads || '1K+';
    const appCategory = app.category || app.categoryBadge || 'Utilities';

    // Use a default icon for API data since we don't have icon components
    const IconComponent = app.icon || (() => (
      <div className="w-8 h-8 bg-fileduck-primary rounded-lg flex items-center justify-center">
        <span className="text-white font-bold text-sm">
          {appName ? appName.charAt(0).toUpperCase() : 'A'}
        </span>
      </div>
    ));

    return (
      <div className="relative">
        <div
          className="flex items-center py-3 px-0 cursor-pointer hover:bg-macos-elevated/30 rounded-lg transition-colors duration-150"
          onClick={() => navigate(`/store/app/${appId}`)}
        >
          {/* App Icon */}
          <div className="w-16 h-16 bg-system-blue/10 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
            {app.icon ? (
              <IconComponent className="h-8 w-8 text-system-blue" />
            ) : (
              <div className="w-10 h-10 bg-system-blue rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {appName ? appName.charAt(0).toUpperCase() : 'A'}
                </span>
              </div>
            )}
          </div>

          {/* App Content */}
          <div className="flex-1 min-w-0 mr-3">
            <h3 className={`${Typography.body} font-medium mb-1 truncate`}>
              {appName}
            </h3>
            <p className={`${Typography.caption} mb-2 truncate`}>
              {appDescription}
            </p>
            <div className="flex items-center space-x-3 text-xs">
              <div className="flex items-center space-x-1">
                <Star className="h-3 w-3 text-system-orange fill-current" />
                <span className="text-macos-text-secondary">{appRating}</span>
              </div>
              <span className="text-macos-text-secondary">{appDownloads}</span>
              <span className="bg-macos-elevated text-macos-text-primary px-2 py-0.5 rounded text-xs font-medium border border-macos-border">
                {appCategory}
              </span>
            </div>
          </div>

          {/* Action Button */}
          <div className="flex-shrink-0" onClick={(e) => e.stopPropagation()}>
            {isUtilityInstalled(appId) ? (
              <MacOSButton
                onClick={() => handleOpen(app)}
                variant="secondary"
                size="sm"
                className="rounded-full px-5 min-w-[60px]"
              >
                Open
              </MacOSButton>
            ) : (
              <MacOSButton
                onClick={() => handleInstall(app)}
                disabled={installing === appId || installationProgress[appId] === 'installing'}
                variant="secondary"
                size="sm"
                className="rounded-full px-5 min-w-[60px] flex items-center justify-center"
              >
                {installationProgress[appId] === 'installing' ? (
                  <div className="w-4 h-4 border-2 border-system-blue border-t-transparent rounded-full animate-spin"></div>
                ) : installationProgress[appId] === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-system-green" />
                ) : installationProgress[appId] === 'failed' ? (
                  'Get'
                ) : (
                  'Get'
                )}
              </MacOSButton>
            )}
          </div>
        </div>

        {/* Divider Line */}
        {showDivider && (
          <div className="absolute bottom-0 left-20 right-0 h-px bg-macos-border"></div>
        )}
      </div>
    );
  };

  return (
    <div className="p-8 h-full overflow-y-auto bg-macos-bg">
      <div
        ref={containerRef}
        className="relative max-w-6xl mx-auto mt-10"
      >
        {/* Sticky Header - appears when scrolled - positioned within content area */}
        <motion.div
          className="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 bg-macos-surface/95 backdrop-blur-md border border-macos-border rounded-xl shadow-lg"
          initial={{ y: -100, opacity: 0 }}
          animate={{
            y: isScrolled ? 20 : -100,
            opacity: isScrolled ? 1 : 0
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          style={{ width: 'min(600px, 90vw)' }}
        >
          <div className="flex items-center justify-between px-6 py-3">
            <h1 className={`${Typography.title3} font-semibold`}>
              Tool Store
            </h1>
            <div className="relative max-w-xs">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-macos-text-tertiary" />
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-macos-border border-0 rounded-lg text-sm text-macos-text-primary placeholder-macos-text-tertiary focus:outline-none focus:ring-2 focus:ring-system-blue/50 focus:bg-macos-surface transition-all duration-200 shadow-inner"
              />
            </div>
          </div>
        </motion.div>
        {/* Animated Header */}
        <motion.div
          ref={headerRef}
          className="mb-10"
          style={{ opacity: headerOpacity }}
        >
          <h1 className={`${Typography.title1} mb-2`}>
            Tool Store
          </h1>
          <motion.p
            className={Typography.bodySecondary}
            style={{ opacity: subtitleOpacity }}
          >
            Discover and install powerful tools to enhance your workflow
          </motion.p>
        </motion.div>

        {/* Animated Search Bar - macOS Style */}
        <motion.div
          className="mb-10"
          style={{ y: searchBarY }}
        >
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-macos-text-tertiary" />
            <input
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2.5 bg-macos-border border-0 rounded-lg text-sm text-macos-text-primary placeholder-macos-text-tertiary focus:outline-none focus:ring-2 focus:ring-system-blue/50 focus:bg-macos-surface transition-all duration-200 shadow-inner"
            />
          </div>
        </motion.div>

          {/* Search Results */}
          {searchQuery && (
            <motion.div
              className="mb-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className={`${Typography.title2} mb-4`}>
                Search Results
              </h2>
              {loadingSearch ? (
                <div className="grid grid-cols-2 gap-x-8">
                  <div className="space-y-0">
                    {Array.from({ length: 2 }).map((_, index) => (
                      <ShimmerCard key={`search-shimmer-left-${index}`} showDivider={index < 1} />
                    ))}
                  </div>
                  <div className="space-y-0">
                    {Array.from({ length: 2 }).map((_, index) => (
                      <ShimmerCard key={`search-shimmer-right-${index}`} showDivider={index < 1} />
                    ))}
                  </div>
                </div>
              ) : getSearchResults().length > 0 ? (
                <div className="grid grid-cols-2 gap-x-8">
                  <div className="space-y-0">
                    {getSearchResults().filter((_, index) => index % 2 === 0).map((app, index, filteredApps) => (
                      <AppCard
                        key={app.app_id || app.id}
                        app={app}
                        showDivider={index < filteredApps.length - 1}
                      />
                    ))}
                  </div>
                  <div className="space-y-0">
                    {getSearchResults().filter((_, index) => index % 2 === 1).map((app, index, filteredApps) => (
                      <AppCard
                        key={app.app_id || app.id}
                        app={app}
                        showDivider={index < filteredApps.length - 1}
                      />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className={Typography.bodySecondary}>
                    No tools found matching your search.
                  </p>
                </div>
              )}
            </motion.div>
          )}

          {/* Store Sections - Only show when not searching */}
          {!searchQuery && (
            <motion.div
              className="space-y-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <StoreSection
                title="Recommended for you"
                sectionType="recommended"
              />

              <StoreSection
                title="See what's trending"
                sectionType="trending"
              />

              <StoreSection
                title="New published"
                sectionType="new"
              />

              <StoreSection
                title="Popular apps"
                sectionType="popular"
              />
            </motion.div>
          )}
      </div>

      {/* Toast Notification */}
      {toast && (
        <motion.div
          className="fixed top-4 right-4 z-50 bg-macos-surface border border-macos-border rounded-lg shadow-lg p-4 max-w-sm"
          initial={{ opacity: 0, y: -50, x: 50 }}
          animate={{ opacity: 1, y: 0, x: 0 }}
          exit={{ opacity: 0, y: -50, x: 50 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-3">
            {toast.type === 'success' && <CheckCircle className="h-5 w-5 text-system-green" />}
            {toast.type === 'error' && <XCircle className="h-5 w-5 text-system-red" />}
            <p className={`${Typography.body} flex-1`}>{toast.message}</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default StoreView;
