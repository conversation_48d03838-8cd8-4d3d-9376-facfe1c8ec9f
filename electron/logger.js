const { Signale } = require('signale');

// Gen-Z emoji configuration for FileDuck Electron logging
const options = {
  displayScope: true,
  displayBadge: true,
  displayDate: false,
  displayFilename: false,
  displayLabel: true,
  displayTimestamp: true,
  underlineLabel: false,
  underlineMessage: false,
  underlinePrefix: false,
  underlineSuffix: false,
  uppercaseLabel: false,
  types: {
    // Success and positive actions
    success: {
      badge: '✨',
      color: 'green',
      label: 'success',
      logLevel: 'info'
    },
    complete: {
      badge: '🎉',
      color: 'green',
      label: 'complete',
      logLevel: 'info'
    },
    // Information and status
    info: {
      badge: '💡',
      color: 'blue',
      label: 'info',
      logLevel: 'info'
    },
    note: {
      badge: '📝',
      color: 'blue',
      label: 'note',
      logLevel: 'info'
    },
    // Process and loading states
    pending: {
      badge: '⏳',
      color: 'yellow',
      label: 'pending',
      logLevel: 'info'
    },
    await: {
      badge: '⌛',
      color: 'yellow',
      label: 'await',
      logLevel: 'info'
    },
    watch: {
      badge: '👀',
      color: 'yellow',
      label: 'watch',
      logLevel: 'info'
    },
    start: {
      badge: '🚀',
      color: 'cyan',
      label: 'start',
      logLevel: 'info'
    },
    // Warnings and cautions
    warn: {
      badge: '⚠️',
      color: 'yellow',
      label: 'warn',
      logLevel: 'warn'
    },
    pause: {
      badge: '⏸️',
      color: 'yellow',
      label: 'pause',
      logLevel: 'warn'
    },
    // Errors and failures
    error: {
      badge: '💥',
      color: 'red',
      label: 'error',
      logLevel: 'error'
    },
    fatal: {
      badge: '💀',
      color: 'red',
      label: 'fatal',
      logLevel: 'error'
    },
    // Debug and development
    debug: {
      badge: '🐛',
      color: 'magenta',
      label: 'debug',
      logLevel: 'debug'
    },
    // Special FileDuck specific loggers
    fileduck: {
      badge: '🦆',
      color: 'cyan',
      label: 'fileduck',
      logLevel: 'info'
    },
    app: {
      badge: '📱',
      color: 'blue',
      label: 'app',
      logLevel: 'info'
    },
    install: {
      badge: '📦',
      color: 'green',
      label: 'install',
      logLevel: 'info'
    },
    uninstall: {
      badge: '🗑️',
      color: 'red',
      label: 'uninstall',
      logLevel: 'info'
    },
    python: {
      badge: '🐍',
      color: 'yellow',
      label: 'python',
      logLevel: 'info'
    },
    file: {
      badge: '📄',
      color: 'blue',
      label: 'file',
      logLevel: 'info'
    },
    api: {
      badge: '🌐',
      color: 'cyan',
      label: 'api',
      logLevel: 'info'
    },
    database: {
      badge: '🗄️',
      color: 'magenta',
      label: 'database',
      logLevel: 'info'
    },
    electron: {
      badge: '⚡',
      color: 'blue',
      label: 'electron',
      logLevel: 'info'
    },
    ipc: {
      badge: '🔗',
      color: 'cyan',
      label: 'ipc',
      logLevel: 'info'
    }
  }
};

// Create the main logger instance
const logger = new Signale(options);

// Export both the configured logger and the Signale class for custom instances
module.exports = {
  logger,
  Signale,
  createLogger: (scope) => logger.scope(scope)
};
