const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const https = require('https');
const http = require('http');
const AdmZip = require('adm-zip');
const {createFolder} = require("../src/utils/common_tasks");
const { createLogger } = require('./logger');

class ContextBridge {
  constructor() {
    this.userDataPath = app.getPath('userData');
    this.utilitiesPath = path.join(this.userDataPath, 'utilities');
    this.installedJsonPath = path.join(this.userDataPath, 'installed.json');
    this.logger = createLogger('contextbridge');
    this.ensureDirectories();
  }

  ensureDirectories() {
    // Ensure userData directory exists
    if (!fs.existsSync(this.userDataPath)) {
      fs.mkdirSync(this.userDataPath, { recursive: true });
    }

    // Ensure utilities directory exists
    if (!fs.existsSync(this.utilitiesPath)) {
      fs.mkdirSync(this.utilitiesPath, { recursive: true });
    }

    // Ensure installed.json exists
    if (!fs.existsSync(this.installedJsonPath)) {
      const initialData = { installed: [] };
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(initialData, null, 2));
    }
  }

  /**
   * Reads /userData/installed.json and returns the installed array
   * Also cleans up orphaned entries
   */
  getInstalledUtilities() {
    try {
      const data = fs.readFileSync(this.installedJsonPath, 'utf8');
      const parsed = JSON.parse(data);
      const utilities = parsed.installed || [];

      // Clean up orphaned entries (database entries without corresponding directories)
      const validUtilities = utilities.filter(utility => {
        const utilityDir = path.dirname(utility.localPath);
        const exists = fs.existsSync(utilityDir);
        if (!exists) {
          this.logger.note(`Removing orphaned database entry for ${utility.id}`);
        }
        return exists;
      });

      // Update the database if we removed any orphaned entries
      if (validUtilities.length !== utilities.length) {
        const updatedData = { installed: validUtilities };
        fs.writeFileSync(this.installedJsonPath, JSON.stringify(updatedData, null, 2));
        this.logger.success(`Cleaned up ${utilities.length - validUtilities.length} orphaned database entries`);
      }

      return validUtilities;
    } catch (error) {
      this.logger.error('Error reading installed.json:', error);
      return [];
    }
  }

  /**
   * Downloads the .zip package from downloadUrl
   * Unzips the package into /userData/utilities/{utility-name-v{version}}
   * Adds a new entry to the /userData/installed.json database
   */
  async installUtility(downloadUrl) {
    try {
      this.logger.install(`Installing utility from ${downloadUrl}`);

      // Download the zip file to temp location
      const tempZipPath = path.join(this.userDataPath, 'temp_download_' + Date.now() + '.zip');
      await this.downloadFile(downloadUrl, tempZipPath);

      const tempExtractPath = createFolder('', true);

      const zip = new AdmZip(tempZipPath);
      zip.extractAllTo(tempExtractPath, true);

      // Read config.json to get metadata
      const configPath = path.join(tempExtractPath, 'config.json')
      if (!fs.existsSync(configPath)) {
        throw new Error('config.json not found in package. Please check ' + tempExtractPath);
      }




      const utilityConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      const { name, version } = utilityConfig;

      if (!name || !version) {
        throw new Error('config.json must contain name and version');
      }

      // Create final utility directory
      const utilityId = `${name.toLowerCase().replace(/\s+/g, '-')}-v${version}`;

      // Log format type after utilityId is defined
      this.logger.info(`Using config.json format for ${utilityId}`);
      const finalUtilityPath = path.join(this.utilitiesPath, utilityId);

      this.logger.info(`Final utility path: ${finalUtilityPath}`);
      if (fs.existsSync(finalUtilityPath)) {
        throw new Error(`Utility ${utilityId} is already installed`);
      }

      // Move extracted files to final location
      fs.renameSync(tempExtractPath, finalUtilityPath);

      // Verify required files exist based on language
      let mainExecutableFound = false;
      let actualMainPath = null;

      if (utilityConfig.language === 'python') {
        // Use the 'main' field, otherwise default to 'main.py'
        const mainFileName = utilityConfig.main || 'main.py';

        // Try multiple possible locations for the main file
        const possiblePaths = [
          path.join(finalUtilityPath, mainFileName),           // Direct path from config
          path.join(finalUtilityPath, 'dist', mainFileName),   // In dist/ subdirectory
          path.join(finalUtilityPath, 'src', mainFileName),    // In src/ subdirectory
          path.join(finalUtilityPath, 'main.py')               // Default fallback
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            mainExecutableFound = true;
            actualMainPath = possiblePath;
            this.logger.success(`Found Python main file at: ${possiblePath}`);
            break;
          }
        }
      } else {
        throw new Error(`Unsupported language: ${utilityConfig.language}. Only 'python' is supported.`);
      }

      if (!mainExecutableFound) {
        const expectedFile = 'main.py';
        throw new Error(`${expectedFile} not found in package for ${utilityConfig.language} utility`);
      }

      // Update installed.json
      const installedData = this.getInstalledData();
      const configFileName = path.basename(configPath);
      const configRelativePath = configPath.includes('dist/') ?
        path.join('dist', configFileName) : configFileName;

      const newEntry = {
        id: utilityId,
        name: utilityConfig.name,
        version: utilityConfig.version,
        installDate: new Date().toISOString(),
        localPath: path.join(finalUtilityPath, configRelativePath),
        language: utilityConfig.language || 'N/A',
        requirements: utilityConfig.requirements || [],
        format: 'config.json'
      };

      this.logger.install(`Installing ${utilityId} with ${configFileName} format`);

      installedData.installed.push(newEntry);
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(installedData, null, 2));

      // Cleanup temp files
      if (fs.existsSync(tempZipPath)) {
        fs.unlinkSync(tempZipPath);
      }

      this.logger.success(`Utility ${name} v${version} installed successfully`);
      return { success: true, utilityId };

    } catch (error) {
      this.logger.error('Install utility error:', error);
      
      // Cleanup on failure
      const tempZipPath = path.join(this.userDataPath, 'temp_download.zip');
      const tempExtractPath = path.join(this.userDataPath, 'temp_extract');
      
      if (fs.existsSync(tempZipPath)) {
        fs.unlinkSync(tempZipPath);
      }
      if (fs.existsSync(tempExtractPath)) {
        fs.rmSync(tempExtractPath, { recursive: true, force: true });
      }
      
      throw error;
    }
  }

  /**
   * Developer-only function that opens a file dialog to select a local .zip
   * and triggers the same installation and database-update logic
   */
  async installUtilityFromLocalPath(localZipPath) {
    alert('Local install not implemented yet');
  }

  /**
   * Finds the utility by id in the installed.json database,
   * detects runtime type (WASM/Python), and returns appropriate execution info
   */
  async runUtility(utilityId, fileData, config) {
    try {
      const installedUtilities = this.getInstalledUtilities();
      const utility = installedUtilities.find(u => u.id === utilityId);

      if (!utility) {
        throw new Error(`Utility ${utilityId} not found`);
      }

      const utilityDir = path.dirname(utility.localPath);

      // Load utility configuration
      const utilityConfig = JSON.parse(fs.readFileSync(utility.localPath, 'utf8'));
      this.logger.info(`Loaded config from: ${utility.localPath}`);
      this.logger.debug(`Config structure:`, utilityConfig);

      // Detect runtime type based on language field
      let runtimeType = 'python'; // default to python
      let executablePath = null;

      if (utilityConfig.language === 'python') {
        // Use spawn-based execution for Python
        const pythonFile = utilityConfig.main || 'main.py';
        runtimeType = 'python-spawn';

        // Try multiple possible locations for the Python file
        const possiblePaths = [
          path.join(utilityDir, pythonFile),           // Direct path
          path.join(utilityDir, 'dist', pythonFile),   // In dist/ subdirectory
          path.join(utilityDir, 'src', pythonFile),    // In src/ subdirectory
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            executablePath = possiblePath;
            this.logger.success(`Found Python executable at: ${possiblePath}`);
            break;
          }
        }
      } else if (utilityConfig.language === 'shell') {
        const possiblePaths = [
          path.join(utilityDir, 'main.sh'),
          path.join(utilityDir, 'dist', 'main.sh'),
          path.join(utilityDir, 'src', 'main.sh')
        ];

        for (const possiblePath of possiblePaths) {
          if (fs.existsSync(possiblePath)) {
            runtimeType = 'shell';
            executablePath = possiblePath;
            this.logger.success(`Found shell executable at: ${possiblePath}`);
            break;
          }
        }
      } else {
        throw new Error(`Unsupported language: ${utilityConfig.language}. Only 'python' and 'shell' are supported.`);
      }

      if (!executablePath) {
        throw new Error(`No executable file found for utility ${utilityId} (expected ${runtimeType} runtime)`);
      }

      this.logger.info(`Utility ${utilityId} detected as ${runtimeType} runtime`);

      return {
        utilityId,
        runtimeType,
        executablePath,
        localPath: utility.localPath,
        config: utilityConfig,
        fileData,
        userConfig: config
      };

    } catch (error) {
      this.logger.error('Run utility error:', error);
      throw error;
    }
  }

  /**
   * Uninstall a utility by removing its files and database entry
   */
  async uninstallUtility(utilityId) {
    try {
      const installedUtilities = this.getInstalledUtilities();
      const utility = installedUtilities.find(u => u.id === utilityId);

      if (!utility) {
        throw new Error(`Utility ${utilityId} not found`);
      }

      const utilityDir = path.dirname(utility.localPath);

      // Remove utility directory
      if (fs.existsSync(utilityDir)) {
        fs.rmSync(utilityDir, { recursive: true, force: true });
        this.logger.success(`Removed utility directory: ${utilityDir}`);
      }

      // Update installed.json
      const installedData = this.getInstalledData();
      installedData.installed = installedData.installed.filter(u => u.id !== utilityId);
      fs.writeFileSync(this.installedJsonPath, JSON.stringify(installedData, null, 2));

      this.logger.uninstall(`Uninstalled utility: ${utilityId}`);
      return { success: true, utilityId };

    } catch (error) {
      this.logger.error('Uninstall utility error:', error);
      throw error;
    }
  }

  // Helper methods
  getInstalledData() {
    try {
      const data = fs.readFileSync(this.installedJsonPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return { installed: [] };
    }
  }

  async downloadFile(url, destinationPath) {
    return new Promise((resolve, reject) => {
      // Handle relative URLs (convert to localhost URL in development)
      if (url.startsWith('/') && !url.startsWith('//')) {
        const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged;
        if (isDev) {
          url = `http://localhost:3000${url}`;
        } else {
          // In production, treat as local file path relative to app resources
          const appPath = require('electron').app.getAppPath();
          const localPath = path.join(appPath, url.substring(1));
          if (fs.existsSync(localPath)) {
            fs.copyFileSync(localPath, destinationPath);
            resolve(destinationPath);
            return;
          } else {
            reject(new Error(`Local file not found: ${localPath}`));
            return;
          }
        }
      }

      // Handle absolute local file paths (for development)
      if (!url.startsWith('http')) {
        if (fs.existsSync(url)) {
          fs.copyFileSync(url, destinationPath);
          resolve(destinationPath);
          return;
        } else {
          reject(new Error(`Local file not found: ${url}`));
          return;
        }
      }

      // Handle HTTP/HTTPS URLs
      const file = fs.createWriteStream(destinationPath);
      const protocol = url.startsWith('https:') ? https : http;

      protocol.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Failed to download: ${response.statusCode}`));
          return;
        }

        response.pipe(file);

        file.on('finish', () => {
          file.close();
          resolve(destinationPath);
        });

        file.on('error', (err) => {
          fs.unlink(destinationPath, () => {}); // Delete the file on error
          reject(err);
        });
      }).on('error', (err) => {
        reject(err);
      });
    });
  }
}

module.exports = ContextBridge;
